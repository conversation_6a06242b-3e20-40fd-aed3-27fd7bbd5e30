const { DateTime } = require("luxon");
const BoostMetric = require("../models/boost-metric");
const BoostTransaction = require('../models/boost-transaction');
const { updateUserScore } = require("../lib/score");
const coinsLib = require("../lib/coins");
const { sendSocketEvent } = require('./socket');
const premiumLib = require('./premium');

async function activateBoostByPurchase(user, durationMinutes) {
  let freeBoostUsed = false;

  if (user.numBoostsFree > 0) {
    user.numBoostsFree -= 1;
    freeBoostUsed = true;
  } else {
    user.numBoosts -= 1;
  }

  if (user.isBoostActive()) {
    const totalDurationMinutes = durationMinutes;

    const newExpirationDate = DateTime.fromJSDate(user.boostExpiration)
      .plus({ minutes: totalDurationMinutes })
      .toJSDate();

    user.boostExpiration = newExpirationDate;
    user.boostDurationMinutes += totalDurationMinutes;
    user.postBoostPopupHandled = undefined;
    user.metrics.numActionsReceivedDuringBoostQuota = user.metrics.numActionsReceivedDuringBoostQuota + 100;
    await user.save();

    const mostRecentBoost = await BoostMetric.findOne({ user: user._id }).sort(
      "-boostExpiration"
    );
    mostRecentBoost.durationMinutes += totalDurationMinutes;
    mostRecentBoost.numBoosts += 1;
    mostRecentBoost.boostExpiration = newExpirationDate;

    if (freeBoostUsed) {
      mostRecentBoost.freeBoostUsed += 1;
    } else {
      mostRecentBoost.purchaseBoostUsed += 1;
    }

    await mostRecentBoost.save();
  } else {
    user.boostExpiration = DateTime.utc()
      .plus({ minutes: durationMinutes })
      .toJSDate();
    user.boostDurationMinutes = durationMinutes;
    user.postBoostPopupHandled = undefined;
    user.metrics.numActionsReceivedDuringBoostQuota = 100;
    await user.save();

    const boostMetric = new BoostMetric({
      user: user._id,
      durationMinutes,
      boostExpiration: user.boostExpiration,
      numBoosts: 1,
      ...(freeBoostUsed ? { freeBoostUsed: 1 } : { purchaseBoostUsed: 1 }),
    });
    await boostMetric.save();
  }

  await updateUserScore(user);

  await user.save();

  await BoostTransaction.create({
    user: user._id,
    newBalance: user.numBoosts ?? 0,
    freeBoostNewBalance: user.numBoostsFree ?? 0,
    description: freeBoostUsed ? 'used free boost' : 'used boost',
    ...(freeBoostUsed
      ? { freeBoostTransactionAmount: -1, transactionAmount: 0 }
      : { transactionAmount: -1, freeBoostTransactionAmount: 0 }),
  });

  return { boostExpiration: user.boostExpiration };
}

module.exports = { activateBoostByPurchase };
