const express = require('express');
const router = express.Router();
const asyncHandler = require('express-async-handler');
const httpErrors = require('../lib/http-errors');
const { iap, processBoostPurchase } = require('../lib/iap');
const appStoreJwsValidator = require('../lib/app-store-connect');
const { activateBoostByPurchase } = require('../lib/boost')

module.exports = function () {
  router.put('/purchase', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const errorMsg = 'Purchase could not be validated.';

    if (!req.body.receipt && !req.body.transactionJws) {
      return next(httpErrors.invalidInputError(errorMsg));
    }

    const validator = req.body.transactionJws ? appStoreJwsValidator : iap;
    const data = req.body.transactionJws ? req.body.transactionJws : req.body.receipt;

    validator.validate(data, async (error, validatedData) => {
      if (error) {
        console.log(error, validatedData);
        if (
          validatedData && validatedData.status == 2 // hacked apple receipt
          || error.message == 'Status:400' // hacked google receipt
          || error.message == 'Status:403' // google receipt from elsewhere
        ) {
          console.log('Potentially a hacked receipt');
          return next(httpErrors.invalidInputError(errorMsg));
        }
        return next(httpErrors.applicationError(errorMsg));
      }

      const purchaseData = validator.getPurchaseData(validatedData);

      console.log('validatedData :', validatedData);
      console.log('purchaseData :', purchaseData);

      await processBoostPurchase(user, purchaseData, req.body.price, req.body.currency)
        .then(() => res.json({}))
        .catch((err) => {
          console.log(err);
          return next(httpErrors.applicationError());
        });
    });
  }));

  router.put('/use', asyncHandler(async (req, res, next) => {
    const numBoosts = (req.user.numBoosts ?? 0) + (req.user.numBoostsFree ?? 0);
    if (numBoosts < 1) {
      return next(httpErrors.forbiddenError('No boost remaining'));
    }

    const activateReturn = await activateBoostByPurchase(
      req.user,
      req.user.getBoostDurationMinutes(),
    );

    req.user.metrics.numBoostUsed += 1;
    req.user.updateEvents({ use_boost: true });
    await req.user.save();

    return res.json({ ...activateReturn });
  }));

  router.put('/activityToken', asyncHandler(async (req, res, next) => {
    const user = req.user;

    user.boostActivityToken = req.body.activityToken;
    await user.save();

    return res.json({});
  }));

  return router;
}
