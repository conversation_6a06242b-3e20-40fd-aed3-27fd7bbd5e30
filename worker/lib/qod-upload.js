const { createQod } = require('../../lib/social');
const googleSheetsManager = require('../../lib/google-sheets-manager');

const QOD_SHEET_ID = process.env.QOD_SHEET_ID || '1sifDQitS0oRE3YGxcmv323CtPyrapPP8TnLcV1s5aaw'

function createQodSheetsClient() {
  return googleSheetsManager.createSheetsClient({
    spreadsheetId: QOD_SHEET_ID,
    hasHeader: true,
    defaults: { sheetName: 'Sheet1', statusColumn: 'C', dataColumns: 'A:C' },
    columnMap: { language: 0, text: 1, status: 2 }
  });
}

function validateQodData(qod) {
  const errors = [];
  const normalizedQod = {};

  const text = qod.text || qod.Text || qod.TEXT;
  if (!text || text.trim() === '') {
    errors.push('Missing required field: text');
  } else {
    normalizedQod.text = text.trim();
  }

  const language = qod.language || qod.Language || qod.LANGUAGE || 'en';
  normalizedQod.language = language.trim();

  return {
    isValid: errors.length === 0,
    errors,
    normalizedQod: errors.length === 0 ? normalizedQod : null,
  };
}

async function markQodAsUploaded(client, rowIndex, uploadedColumn = 'C') {
  await googleSheetsManager.updateCell(client, {
    rowNumber: rowIndex,
    value: 'UPLOAD-COMPLETED',
    statusColumn: uploadedColumn,
  });
}

async function executeQodUploadSheet(req, res) {
  try {
    // if (!process.env.TESTING) {
    //   res.json({});
    // }

    const qodSheetClient = createQodSheetsClient();
    const pendingRows = await googleSheetsManager.getPendingRows(qodSheetClient, {
      filterValue: 'UPLOAD-COMPLETED',
    });

    if (pendingRows.length > 0) await processQodData(qodSheetClient, pendingRows);
    return 
    if (process.env.TESTING) {
      res.json({});
    }

  } catch (err) {
    console.error('QOD upload failed:', err);
    return res.status(500).json({ error: err.message });
  }
}

async function processQodData(qodSheetClient, pendingRows) {
  try {
    // 1 seconds between requests to stay under 60 per minute as there free tier limit to 60 writes per minute
    const delay = 1000; 
    
    for (const qodData of pendingRows) {
      try {
        const validation = validateQodData(qodData);

        if (!validation.isValid) continue;

        const question = await createQod({
          text: validation.normalizedQod.text,
          createdBy: null,
          language: validation.normalizedQod.language,
        });

        await markQodAsUploaded(qodSheetClient, qodData.rowNumber);

        await new Promise(resolve => setTimeout(resolve, delay));
      } catch (error) {
        console.error(`Failed to process row ${qodData.rowNumber}:`, error);
      }
    }

    console.log(`Completed processing ${pendingRows.length} QODs`);
  } catch (error) {
    console.error('Error in processQodData:', error);
  }
}

module.exports = {
  executeQodUploadSheet,
  processQodData,
  validateQodData,
  markQodAsUploaded,
};
