const { expect, assert } = require('chai');
const { initApp } = require('./helper/api');
const Question = require('../models/question');
const { DateTime } = require('luxon');
const sinon = require('sinon');
const googleSheetsManager = require('../lib/google-sheets-manager');

const { createQod } = require('../lib/social');
const { purgeQods } = require('../lib/temp/purgeQods');

const getNextQodDate = (lastQodDate) => DateTime.fromJSDate(lastQodDate).plus({ days: 1 }).toJSDate();

it('purges future qods', async function () {

  //create 3 users
  for (let i = 0; i < 3; i++) {
    await initApp(i);
  }

  //create Qod before start time
  const preQodId = (await createQod({
    createdBy: 1,
    text: `texts`,
    language: 'en'
  }))._id;

  const startTime = new Date();
  const qIds = [];
  {
    const createdBy = [0, 1, null, 0, 0, 2, 1, 1, null];
    for (let i = 0; i < createdBy.length; i++) {
      const qod = await createQod({
        createdBy: createdBy[i],
        text: `text${i}`,
        language: 'en'
      });
      qIds.push(qod._id);
    }
  }

  //create qod with another language
  const diffLangQodId = (await createQod({
    createdBy: 1,
    text: `textss`,
    language: 'de'
  }))._id;

  //purge
  await purgeQods(startTime);
  {
    //fetch all qods
    const allQods = await Question.find({ interestName: 'questions' }, { _id: 1 }).sort({ _id: 1 });
    assert(allQods[0]._id.equals(preQodId));
    assert(allQods[6]._id.equals(diffLangQodId));
  }

  //fetch only the future en qods
  const futureQods = await Question.find({ interestName: 'questions', language: 'en', createdAt: { $gt: startTime } }, { _id: 1, createdBy: 1, createdAt: 1 }).sort({createdAt:1});

  console.log(futureQods);

  expect(futureQods.length).to.eql(5);

  expect(futureQods[0].createdBy).to.eql('0');
  assert(futureQods[0]._id.equals(qIds[0]));

  lastCreatedAt = getNextQodDate(futureQods[0].createdAt);
  expect(futureQods[1].createdBy).to.eql('1');
  assert(futureQods[1]._id.equals(qIds[1]));
  expect(futureQods[1].createdAt).to.eql(lastCreatedAt);

  lastCreatedAt = getNextQodDate(futureQods[1].createdAt);
  expect(futureQods[2].createdBy).to.eql(null);
  assert(futureQods[2]._id.equals(qIds[2]));
  expect(futureQods[2].createdAt).to.eql(lastCreatedAt);

  lastCreatedAt = getNextQodDate(futureQods[2].createdAt);
  expect(futureQods[3].createdBy).to.eql('2');
  assert(futureQods[3]._id.equals(qIds[5]));
  expect(futureQods[3].createdAt).to.eql(lastCreatedAt);

  lastCreatedAt = getNextQodDate(futureQods[3].createdAt);
  expect(futureQods[4].createdBy).to.eql(null);
  assert(futureQods[4]._id.equals(qIds[8]));
  expect(futureQods[4].createdAt).to.eql(lastCreatedAt);

})

describe('executeQodUploadSheet worker', function () {
  let mockReq, mockRes;
  let createSheetsClientStub, getPendingRowsStub, updateCellStub;

  beforeEach(function () {
    // Create mock request and response objects
    mockReq = {};
    mockRes = {
      json: sinon.spy(),
      status: sinon.stub().returnsThis()
    };

    // Set up default stubs for Google Sheets functions BEFORE requiring the worker
    createSheetsClientStub = sinon.stub(googleSheetsManager, 'createSheetsClient').returns({
      sheets: {},
      spreadsheetId: 'test-sheet-id',
      defaults: { sheetName: 'Sheet1', statusColumn: 'C', dataColumns: 'A:C' },
      hasHeader: true,
      columnMap: { language: 0, text: 1, status: 2 }
    });

    getPendingRowsStub = sinon.stub(googleSheetsManager, 'getPendingRows').resolves([]);
    updateCellStub = sinon.stub(googleSheetsManager, 'updateCell').resolves();
  });

  afterEach(function () {
    if (createSheetsClientStub) createSheetsClientStub.restore();
    if (getPendingRowsStub) getPendingRowsStub.restore();
    if (updateCellStub) updateCellStub.restore();
  });

  it('should process pending QOD rows successfully', async function () {
    // Mock 3 pending rows
    const mockPendingRows = [
      {
        language: 'en',
        text: 'What is your favorite color?',
        status: '',
        rowNumber: 2
      },
      {
        language: 'en',
        text: 'What makes you happy?',
        status: '',
        rowNumber: 3
      },
      {
        language: 'es',
        text: 'What is your favorite food?',
        status: '',
        rowNumber: 4
      }
    ];

    // Set up the stubs for this test
    getPendingRowsStub.resolves(mockPendingRows);
    updateCellStub.resolves();

    // Require the worker AFTER setting up stubs
    const { executeQodUploadSheet } = require('../worker/lib/qod-upload');

    // Execute the worker
    await executeQodUploadSheet(mockReq, mockRes);

    // Verify the response
    expect(mockRes.json.calledOnce).to.be.true;
    expect(mockRes.json.calledWith({})).to.be.true;

    // Verify Google Sheets functions were called correctly
    expect(getPendingRowsStub.calledOnce).to.be.true;
    expect(getPendingRowsStub.calledWith(sinon.match.any, { filterValue: 'UPLOAD-COMPLETED' })).to.be.true;

    // Verify updateCell was called for each row
    expect(updateCellStub.callCount).to.equal(3);
    expect(updateCellStub.getCall(0).args[1]).to.deep.include({
      rowNumber: 2,
      value: 'UPLOAD-COMPLETED',
      statusColumn: 'C'
    });
    expect(updateCellStub.getCall(1).args[1]).to.deep.include({
      rowNumber: 3,
      value: 'UPLOAD-COMPLETED',
      statusColumn: 'C'
    });
    expect(updateCellStub.getCall(2).args[1]).to.deep.include({
      rowNumber: 4,
      value: 'UPLOAD-COMPLETED',
      statusColumn: 'C'
    });

    const createdQods = await Question.find({
      interestName: 'questions',
      isQod: true,
      text: { $in: ['What is your favorite color?', 'What makes you happy?', 'What is your favorite food?'] }
    });

    expect(createdQods.length).to.equal(3);
  });

  it('should handle rows with UPLOAD-COMPLETED status (no processing needed)', async function () {
    const mockCompletedRows = [];

    // Set up the stubs for this test
    getPendingRowsStub.resolves(mockCompletedRows);

    // Require the worker AFTER setting up stubs
    const { executeQodUploadSheet } = require('../worker/lib/qod-upload');

    // Execute the worker
    await executeQodUploadSheet(mockReq, mockRes);

    // Verify the response
    expect(mockRes.json.calledOnce).to.be.true;
    expect(mockRes.json.calledWith({})).to.be.true;

    // Verify Google Sheets functions were called correctly
    expect(getPendingRowsStub.calledOnce).to.be.true;
    expect(getPendingRowsStub.calledWith(sinon.match.any, { filterValue: 'UPLOAD-COMPLETED' })).to.be.true;

    // Verify updateCell was not called since no rows to process
    expect(updateCellStub.callCount).to.equal(0);

    // Verify no new QODs were created since no rows to process
    const qodCountBefore = await Question.countDocuments({ interestName: 'questions', isQod: true });
    expect(qodCountBefore).to.be.at.least(0);
  });

  it('should handle validation errors gracefully', async function () {
    // Mock rows with invalid data
    const mockInvalidRows = [
      {
        language: 'en',
        text: '', // Empty text should fail validation
        status: '',
        rowNumber: 2
      },
      {
        language: 'en',
        text: 'Valid question text',
        status: '',
        rowNumber: 3
      }
    ];

    // Set up the stubs for this test
    getPendingRowsStub.resolves(mockInvalidRows);
    updateCellStub.resolves();

    // Require the worker AFTER setting up stubs
    const { executeQodUploadSheet } = require('../worker/lib/qod-upload');

    // Execute the worker
    await executeQodUploadSheet(mockReq, mockRes);

    // Verify the response
    expect(mockRes.json.calledOnce).to.be.true;
    expect(mockRes.json.calledWith({})).to.be.true;

    // Verify Google Sheets functions were called correctly
    expect(getPendingRowsStub.calledOnce).to.be.true;

    // Verify updateCell was only called for the valid row (row 3)
    expect(updateCellStub.callCount).to.equal(1);
    expect(updateCellStub.getCall(0).args[1]).to.deep.include({
      rowNumber: 3,
      value: 'UPLOAD-COMPLETED',
      statusColumn: 'C'
    });

    const createdQods = await Question.find({
      interestName: 'questions',
      isQod: true,
    });
    expect(createdQods.length).to.equal(1);

  });

  it('should handle Google Sheets API errors', async function () {
    // Set up the stub to throw an error
    getPendingRowsStub.rejects(new Error('Google Sheets API error'));

    // Require the worker AFTER setting up stubs
    const { executeQodUploadSheet } = require('../worker/lib/qod-upload');

    // Execute the worker
    await executeQodUploadSheet(mockReq, mockRes);

    // Verify error response
    expect(mockRes.status.calledWith(500)).to.be.true;
    expect(mockRes.json.calledWith({ error: 'Google Sheets API error' })).to.be.true;

    // Verify getPendingRows was called
    expect(getPendingRowsStub.calledOnce).to.be.true;

    // Verify updateCell was not called due to the error
    expect(updateCellStub.callCount).to.equal(0);
  });
});