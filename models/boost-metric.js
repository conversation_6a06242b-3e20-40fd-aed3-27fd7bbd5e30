const mongoose = require('mongoose');

const boostMetricSchema = new mongoose.Schema({
  createdAt: { type: Date, default: Date.now },
  user: { type: String, ref: 'User' },
  durationMinutes: { type: Number },
  coinsSpent: { type: Number },
  purchaseBoostUsed: { type: Number, default: 0 },
  freeBoostUsed: { type: Number },
  numBoosts: { type: Number },
  boostExpiration: { type: Date },
  numActionsReceived: { type: Number, default: 0 },
  numLikesReceived: { type: Number, default: 0 },
  numLocalActionsReceived: { type: Number, default: 0 },
  numLocalLikesReceived: { type: Number, default: 0 },
  numViewsReceived: { type: Number },
  postBoostNumActionsReceived: { type: Number, default: 0 },
  postBoostNumLikesReceived: { type: Number, default: 0 },
  boostEffectiveness: { type: Number },
});

// Indexes
// ===========================================================================

boostMetricSchema.index({
  user: 1,
  boostExpiration: -1,
});

// Methods
// ===========================================================================

boostMetricSchema.statics.incrementMetrics = async function (userId, metrics) {
  const incrementParams = {};
  for (const metric of metrics) {
    if (metric in boostMetricSchema.obj) {
      incrementParams[metric] = 1;
    }
  }
  const mostRecentBoost = await this.findOne({ user: userId }).sort('-boostExpiration');
  await this.updateOne(
    { _id: mostRecentBoost._id },
    { $inc: incrementParams },
  );
};

// Export schema =============================================================
module.exports = mongoose.model('BoostMetric', boostMetricSchema);
